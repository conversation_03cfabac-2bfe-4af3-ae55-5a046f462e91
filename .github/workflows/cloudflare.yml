name: API
on:
  push:
    branches:
      - main
    paths:
      - 'src/**'
      - '.github/workflows/cloudflare.yml'
      - 'package.json'
      - 'tsconfig.json'
      - 'vitest.config.ts'
      - 'wrangler.toml'

jobs:
  deploy:
    name: Deploy api cloudflare
    runs-on: ubuntu-latest
    timeout-minutes: 60

    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup node
        uses: actions/setup-node@v4
        with:
          cache: "npm"

      - name: Install dependencies
        run: npm ci --quiet --no-progress

      - name: Publish
        run: npm run deploy
        env:
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
