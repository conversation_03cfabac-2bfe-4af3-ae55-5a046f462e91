{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "WebWorker"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "strict": true, "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "forceConsistentCasingInFileNames": true, "types": ["@cloudflare/workers-types", "node"]}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist"]}